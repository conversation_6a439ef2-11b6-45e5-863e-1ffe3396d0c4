"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("./guard/jwt-auth.guard");
const jwt_1 = require("@nestjs/jwt");
const express_1 = require("express");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    app.enableCors({
        origin: configService.get('FRONTEND_URL') || 'http://localhost:3001',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
        whitelist: true,
    }));
    app.useGlobalGuards(new jwt_auth_guard_1.JwtAuthGuard(app.get(jwt_1.JwtService)));
    app.use((0, express_1.json)({ limit: '50mb' }));
    app.use((0, express_1.urlencoded)({ extended: true, limit: '50mb' }));
    const config = new swagger_1.DocumentBuilder()
        .setTitle('StudyBuddy API')
        .setDescription(`
      StudyBuddy is an educational platform that provides AI-powered chat assistance,
      quiz management, and learning analytics for students.

      ## Authentication
      Most endpoints require JWT authentication. Include the JWT token in the Authorization header:
      \`Authorization: Bearer <your-jwt-token>\`

      ## Admin Access
      Some endpoints require admin privileges. Admin users have elevated permissions for managing subjects, quizzes, and user feedback.
    `)
        .setVersion('1.0.0')
        .setContact('StudyBuddy API Support', '', '<EMAIL>')
        .setLicense('MIT', 'https://opensource.org/licenses/MIT')
        .addServer('http://localhost:3000', 'Development server')
        .addServer('https://api.studybuddy.com', 'Production server')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
    }, 'JWT-auth')
        .addTag('Authentication', 'User authentication and registration')
        .addTag('Chat', 'AI-powered educational chat features')
        .addTag('Users', 'User management and profile operations')
        .addTag('Feedback', 'User feedback and support system')
        .addTag('Admin - Subjects', 'Subject management (Admin only)')
        .addTag('Admin - Topics', 'Topic management (Admin only)')
        .addTag('Admin - Quizzes', 'Quiz management (Admin only)')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
            persistAuthorization: true,
            tagsSorter: 'alpha',
            operationsSorter: 'alpha',
        },
        customSiteTitle: 'StudyBuddy API Documentation',
        customfavIcon: '/favicon.ico',
        customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info .title { color: #3b82f6 }
    `,
    });
    await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
//# sourceMappingURL=main.js.map
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/sidebar */ \"(app-pages-browser)/./src/components/admin/sidebar.tsx\");\n/* harmony import */ var _components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/top-nav */ \"(app-pages-browser)/./src/components/admin/top-nav.tsx\");\n/* harmony import */ var _components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/data-table */ \"(app-pages-browser)/./src/components/admin/data-table.tsx\");\n/* harmony import */ var _components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/add-user-modal */ \"(app-pages-browser)/./src/components/admin/add-user-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-copy.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/admin/student-details-modal */ \"(app-pages-browser)/./src/components/admin/student-details-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingUsers, setIsLoadingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [decryptionCache, setDecryptionCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const hasFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [selectedUserForDetails, setSelectedUserForDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUserDetailsModalOpen, setIsUserDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const columns = [\n        {\n            key: \"email\",\n            label: \"Mail\"\n        },\n        {\n            key: \"decryptedPassword\",\n            label: \"Password\"\n        }\n    ];\n    const copyUserDetails = (email, password)=>{\n        navigator.clipboard.writeText(\"Email: \".concat(email, \"\\nPassword: \").concat(password));\n        toast({\n            title: \"Copied!\",\n            description: \"User details copied to clipboard\"\n        });\n    };\n    const deleteUser = async (userId)=>{\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/users?id=\").concat(userId), {\n                method: 'DELETE',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) throw new Error('Failed to delete user');\n            setUsers(users.filter((user)=>user._id !== userId));\n            toast({\n                title: 'Success',\n                description: 'User deleted successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to delete user:', err);\n            toast({\n                title: 'Error',\n                description: 'Could not delete user'\n            });\n        }\n    };\n    const actions = [\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'View Details',\n            onClick: (row)=>{\n                setSelectedUserForDetails(row);\n                setIsUserDetailsModalOpen(true);\n            },\n            variant: 'view'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            label: 'Copy',\n            onClick: (row)=>copyUserDetails(row.email, row.decryptedPassword || row.password),\n            variant: 'edit'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            label: 'Delete',\n            onClick: (row)=>deleteUser(row._id),\n            variant: 'delete'\n        }\n    ];\n    const refreshUsers = ()=>{\n        fetchUsers();\n    };\n    const handleAddUser = async (param)=>{\n        let { email, password } = param;\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/auth/register\"), {\n                method: 'POST',\n                headers: getAuthHeaders(),\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) throw new Error('Registration failed');\n            const newUser = await response.json();\n            const decryptedPassword = await decryptPassword(password);\n            setUsers([\n                ...users,\n                {\n                    _id: newUser._id,\n                    email,\n                    password,\n                    decryptedPassword\n                }\n            ]);\n            toast({\n                title: 'Success',\n                description: 'User registered successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to register user:', err);\n            toast({\n                title: 'Error',\n                description: 'Failed to register user'\n            });\n        }\n    };\n    // Token retrieval\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (true) {\n                const stored = localStorage.getItem('accessToken');\n                if (!stored) {\n                    router.push('/admin-login');\n                } else {\n                    setToken(stored);\n                }\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        router\n    ]);\n    const getAuthHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[getAuthHeaders]\": ()=>({\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'application/json'\n            })\n    }[\"AdminDashboard.useCallback[getAuthHeaders]\"], [\n        token\n    ]);\n    const decryptPassword = async (encrypted)=>{\n        // Return early if no encrypted password or empty string\n        if (!encrypted || encrypted.trim() === '') {\n            return encrypted;\n        }\n        // Check cache first\n        if (decryptionCache.has(encrypted)) {\n            return decryptionCache.get(encrypted);\n        }\n        try {\n            const res = await fetch('/api/decrypt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    encryptedPassword: encrypted\n                })\n            });\n            if (!res.ok) throw new Error('decrypt');\n            const data = await res.json();\n            // Cache the result only if component is still mounted\n            if (isMountedRef.current) {\n                setDecryptionCache((prev)=>new Map(prev).set(encrypted, data.decryptedPassword));\n            }\n            return data.decryptedPassword;\n        } catch (error) {\n            console.error('Decryption failed for:', encrypted, error);\n            return encrypted;\n        }\n    };\n    const fetchUsers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[fetchUsers]\": async ()=>{\n            if (isLoadingUsers || !token) return;\n            setIsLoadingUsers(true);\n            try {\n                const headers = getAuthHeaders();\n                const res = await fetch(\"\".concat(\"http://localhost:3000\", \"/users\"), {\n                    headers\n                });\n                if (!res.ok) {\n                    const errorText = await res.text();\n                    console.error(\"Admin page - Users fetch failed:\", errorText);\n                    throw new Error(\"fetch failed: \".concat(res.status));\n                }\n                const data = await res.json();\n                const list = await Promise.all(data.map({\n                    \"AdminDashboard.useCallback[fetchUsers]\": async (u)=>({\n                            ...u,\n                            decryptedPassword: await decryptPassword(u.password)\n                        })\n                }[\"AdminDashboard.useCallback[fetchUsers]\"]));\n                if (isMountedRef.current) {\n                    setUsers(list);\n                }\n            } catch (error) {\n                console.error(\"Admin page - Error fetching users:\", error);\n                if (isMountedRef.current) {\n                    toast({\n                        title: 'Error',\n                        description: 'Failed to load users'\n                    });\n                }\n            } finally{\n                if (isMountedRef.current) {\n                    setIsLoadingUsers(false);\n                }\n            }\n        }\n    }[\"AdminDashboard.useCallback[fetchUsers]\"], [\n        token\n    ]) // Only depend on token to prevent infinite loops\n    ;\n    // Fetch users when token is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (token) {\n                fetchUsers();\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        token,\n        fetchUsers\n    ]);\n    // Cleanup effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            return ({\n                \"AdminDashboard.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"AdminDashboard.useEffect\"];\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const pageSize = 10;\n    const breadcrumbs = [\n        {\n            label: \"Admin Dashboard\",\n            href: \"/dashboard\"\n        },\n        {\n            label: \"Student Details\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: \"Admin Dashboard\",\n                        breadcrumbs: breadcrumbs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            title: \"Students\",\n                            count: users.length,\n                            columns: columns,\n                            data: users,\n                            actions: actions,\n                            onAddNew: ()=>setIsModalOpen(true),\n                            addButtonLabel: \"Add User\",\n                            page: page,\n                            pageSize: pageSize,\n                            onPageChange: setPage,\n                            isLoading: isLoadingUsers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                onSubmit: handleAddUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isUserDetailsModalOpen,\n                onClose: ()=>setIsUserDetailsModalOpen(false),\n                student: selectedUserForDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"EfzIiPc10dm7KT7ZBgYdJaGC8p0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0U7QUFDckI7QUFDSztBQUNEO0FBQ007QUFDTztBQUNIO0FBQ2lCO0FBQzlCO0FBQ0s7QUFTbEMsU0FBU2U7O0lBQ3RCLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDa0IsT0FBT0MsU0FBUyxHQUFHbkIsK0NBQVFBLENBQVMsRUFBRTtJQUM3QyxNQUFNLENBQUNvQixPQUFPQyxTQUFTLEdBQUdyQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDc0IsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3dCLGlCQUFpQkMsbUJBQW1CLEdBQUd6QiwrQ0FBUUEsQ0FBc0IsSUFBSTBCO0lBQ2hGLE1BQU1DLGVBQWV4Qiw2Q0FBTUEsQ0FBQztJQUM1QixNQUFNeUIsZ0JBQWdCekIsNkNBQU1BLENBQUM7SUFFN0IsTUFBTTBCLFNBQVN6QiwwREFBU0E7SUFDeEIsTUFBTSxFQUFFMEIsS0FBSyxFQUFFLEdBQUdqQiwwREFBUUE7SUFFMUIsTUFBTSxDQUFDa0Isd0JBQXdCQywwQkFBMEIsR0FBR2hDLCtDQUFRQSxDQUFjO0lBQ2xGLE1BQU0sQ0FBQ2lDLHdCQUF3QkMsMEJBQTBCLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUVyRSxNQUFNbUMsVUFBVTtRQUNkO1lBQUVDLEtBQUs7WUFBU0MsT0FBTztRQUFPO1FBQzlCO1lBQUVELEtBQUs7WUFBcUJDLE9BQU87UUFBVztLQUMvQztJQUVELE1BQU1DLGtCQUFrQixDQUFDQyxPQUFlQztRQUN0Q0MsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUMsVUFBOEJILE9BQXBCRCxPQUFNLGdCQUF1QixPQUFUQztRQUM1RFYsTUFBTTtZQUFFYyxPQUFPO1lBQVdDLGFBQWE7UUFBbUM7SUFDNUU7SUFFQSxNQUFNQyxhQUFhLE9BQU9DO1FBQ3hCLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sR0FBK0NGLE9BQTVDRyx1QkFBK0IsRUFBQyxjQUFtQixPQUFQSCxTQUFVO2dCQUNwRk0sUUFBUTtnQkFDUkMsU0FBU0M7WUFDWDtZQUNBLElBQUksQ0FBQ1AsU0FBU1EsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUNsQ3RDLFNBQVNELE1BQU13QyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLEdBQUcsS0FBS2I7WUFDM0NqQixNQUFNO2dCQUFFYyxPQUFPO2dCQUFXQyxhQUFhO1lBQTRCO1lBQ25FLDhDQUE4QztZQUM5Q2dCLFdBQVcsSUFBTUMsZ0JBQWdCO1FBQ25DLEVBQUUsT0FBT0MsS0FBSztZQUNaQyxRQUFRQyxLQUFLLENBQUMsMEJBQTBCRjtZQUN4Q2pDLE1BQU07Z0JBQUVjLE9BQU87Z0JBQVNDLGFBQWE7WUFBd0I7UUFDL0Q7SUFDRjtJQUVBLE1BQU1xQixVQUFVO1FBQ2Q7WUFDRUMsTUFBTTFELHFHQUFHQTtZQUNUNEIsT0FBTztZQUNQK0IsU0FBUyxDQUFDQztnQkFDUnJDLDBCQUEwQnFDO2dCQUMxQm5DLDBCQUEwQjtZQUM1QjtZQUNBb0MsU0FBUztRQUNYO1FBQ0E7WUFDRUgsTUFBTXpELHFHQUFhQTtZQUNuQjJCLE9BQU87WUFDUCtCLFNBQVMsQ0FBQ0MsTUFBYy9CLGdCQUFnQitCLElBQUk5QixLQUFLLEVBQUU4QixJQUFJRSxpQkFBaUIsSUFBSUYsSUFBSTdCLFFBQVE7WUFDeEY4QixTQUFTO1FBQ1g7UUFDQTtZQUNFSCxNQUFNeEQscUdBQU1BO1lBQ1owQixPQUFPO1lBQ1ArQixTQUFTLENBQUNDLE1BQWN2QixXQUFXdUIsSUFBSVQsR0FBRztZQUMxQ1UsU0FBUztRQUNYO0tBQ0Q7SUFFRCxNQUFNUixlQUFlO1FBQ25CVTtJQUNGO0lBRUEsTUFBTUMsZ0JBQWdCO1lBQU8sRUFBRWxDLEtBQUssRUFBRUMsUUFBUSxFQUF1QztRQUNuRixJQUFJO1lBQ0YsTUFBTVEsV0FBVyxNQUFNQyxNQUFNLEdBQW1DLE9BQWhDQyx1QkFBK0IsRUFBQyxtQkFBaUI7Z0JBQy9FRyxRQUFRO2dCQUNSQyxTQUFTQztnQkFDVG1CLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRXJDO29CQUFPQztnQkFBUztZQUN6QztZQUNBLElBQUksQ0FBQ1EsU0FBU1EsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUNsQyxNQUFNb0IsVUFBVSxNQUFNN0IsU0FBUzhCLElBQUk7WUFDbkMsTUFBTVAsb0JBQW9CLE1BQU1RLGdCQUFnQnZDO1lBQ2hEckIsU0FBUzttQkFBSUQ7Z0JBQU87b0JBQUUwQyxLQUFLaUIsUUFBUWpCLEdBQUc7b0JBQUVyQjtvQkFBT0M7b0JBQVUrQjtnQkFBa0I7YUFBRTtZQUM3RXpDLE1BQU07Z0JBQUVjLE9BQU87Z0JBQVdDLGFBQWE7WUFBK0I7WUFDdEUsOENBQThDO1lBQzlDZ0IsV0FBVyxJQUFNQyxnQkFBZ0I7UUFDbkMsRUFBRSxPQUFPQyxLQUFLO1lBQ1pDLFFBQVFDLEtBQUssQ0FBQyw0QkFBNEJGO1lBQzFDakMsTUFBTTtnQkFBRWMsT0FBTztnQkFBU0MsYUFBYTtZQUEwQjtRQUNqRTtJQUNGO0lBRUEsa0JBQWtCO0lBQ2xCNUMsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSSxJQUE2QixFQUFFO2dCQUNqQyxNQUFNK0UsU0FBU0MsYUFBYUMsT0FBTyxDQUFDO2dCQUNwQyxJQUFJLENBQUNGLFFBQVE7b0JBQ1huRCxPQUFPc0QsSUFBSSxDQUFDO2dCQUNkLE9BQU87b0JBQ0w5RCxTQUFTMkQ7Z0JBQ1g7WUFDRjtRQUNGO21DQUFHO1FBQUNuRDtLQUFPO0lBRVgsTUFBTTBCLGlCQUFpQnJELGtEQUFXQTtzREFBQyxJQUFPO2dCQUN4Q2tGLGVBQWUsVUFBZ0IsT0FBTmhFO2dCQUN6QixnQkFBZ0I7WUFDbEI7cURBQUk7UUFBQ0E7S0FBTTtJQUVYLE1BQU0yRCxrQkFBa0IsT0FBT007UUFDN0Isd0RBQXdEO1FBQ3hELElBQUksQ0FBQ0EsYUFBYUEsVUFBVUMsSUFBSSxPQUFPLElBQUk7WUFDekMsT0FBT0Q7UUFDVDtRQUVBLG9CQUFvQjtRQUNwQixJQUFJN0QsZ0JBQWdCK0QsR0FBRyxDQUFDRixZQUFZO1lBQ2xDLE9BQU83RCxnQkFBZ0JnRSxHQUFHLENBQUNIO1FBQzdCO1FBRUEsSUFBSTtZQUNGLE1BQU1JLE1BQU0sTUFBTXhDLE1BQU0sZ0JBQWdCO2dCQUN0Q0ksUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q29CLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRWMsbUJBQW1CTDtnQkFBVTtZQUN0RDtZQUNBLElBQUksQ0FBQ0ksSUFBSWpDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDN0IsTUFBTWtDLE9BQU8sTUFBTUYsSUFBSVgsSUFBSTtZQUUzQixzREFBc0Q7WUFDdEQsSUFBSW5ELGFBQWFpRSxPQUFPLEVBQUU7Z0JBQ3hCbkUsbUJBQW1Cb0UsQ0FBQUEsT0FBUSxJQUFJbkUsSUFBSW1FLE1BQU1DLEdBQUcsQ0FBQ1QsV0FBV00sS0FBS3BCLGlCQUFpQjtZQUNoRjtZQUVBLE9BQU9vQixLQUFLcEIsaUJBQWlCO1FBQy9CLEVBQUUsT0FBT04sT0FBTztZQUNkRCxRQUFRQyxLQUFLLENBQUMsMEJBQTBCb0IsV0FBV3BCO1lBQ25ELE9BQU9vQjtRQUNUO0lBQ0Y7SUFFQSxNQUFNYixhQUFhdEUsa0RBQVdBO2tEQUFDO1lBQzdCLElBQUlvQixrQkFBa0IsQ0FBQ0YsT0FBTztZQUU5Qkcsa0JBQWtCO1lBQ2xCLElBQUk7Z0JBQ0YsTUFBTStCLFVBQVVDO2dCQUNoQixNQUFNa0MsTUFBTSxNQUFNeEMsTUFBTSxHQUFtQyxPQUFoQ0MsdUJBQStCLEVBQUMsV0FBUztvQkFBRUk7Z0JBQVE7Z0JBRTlFLElBQUksQ0FBQ21DLElBQUlqQyxFQUFFLEVBQUU7b0JBQ1gsTUFBTXVDLFlBQVksTUFBTU4sSUFBSU8sSUFBSTtvQkFDaENoQyxRQUFRQyxLQUFLLENBQUMsb0NBQW9DOEI7b0JBQ2xELE1BQU0sSUFBSXRDLE1BQU0saUJBQTRCLE9BQVhnQyxJQUFJUSxNQUFNO2dCQUM3QztnQkFFQSxNQUFNTixPQUFlLE1BQU1GLElBQUlYLElBQUk7Z0JBQ25DLE1BQU1vQixPQUFPLE1BQU1DLFFBQVFDLEdBQUcsQ0FDNUJULEtBQUtVLEdBQUc7OERBQUMsT0FBTUMsSUFBTTs0QkFBRSxHQUFHQSxDQUFDOzRCQUFFL0IsbUJBQW1CLE1BQU1RLGdCQUFnQnVCLEVBQUU5RCxRQUFRO3dCQUFFOztnQkFHcEYsSUFBSWIsYUFBYWlFLE9BQU8sRUFBRTtvQkFDeEJ6RSxTQUFTK0U7Z0JBQ1g7WUFDRixFQUFFLE9BQU9qQyxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsc0NBQXNDQTtnQkFDcEQsSUFBSXRDLGFBQWFpRSxPQUFPLEVBQUU7b0JBQ3hCOUQsTUFBTTt3QkFBRWMsT0FBTzt3QkFBU0MsYUFBYTtvQkFBdUI7Z0JBQzlEO1lBQ0YsU0FBVTtnQkFDUixJQUFJbEIsYUFBYWlFLE9BQU8sRUFBRTtvQkFDeEJyRSxrQkFBa0I7Z0JBQ3BCO1lBQ0Y7UUFDRjtpREFBRztRQUFDSDtLQUFNLEVBQUUsaURBQWlEOztJQUU3RCxzQ0FBc0M7SUFDdENuQixnREFBU0E7b0NBQUM7WUFDUixJQUFJbUIsT0FBTztnQkFDVG9EO1lBQ0Y7UUFDRjttQ0FBRztRQUFDcEQ7UUFBT29EO0tBQVc7SUFFdEIsaUJBQWlCO0lBQ2pCdkUsZ0RBQVNBO29DQUFDO1lBQ1I7NENBQU87b0JBQ0wwQixhQUFhaUUsT0FBTyxHQUFHO2dCQUN6Qjs7UUFDRjttQ0FBRyxFQUFFO0lBRUwsTUFBTSxDQUFDVyxNQUFNQyxRQUFRLEdBQUd4RywrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNeUcsV0FBVztJQUVqQixNQUFNQyxjQUFjO1FBQUM7WUFBRXJFLE9BQU87WUFBbUJzRSxNQUFNO1FBQWE7UUFBRztZQUFFdEUsT0FBTztRQUFrQjtLQUFFO0lBRXBHLHFCQUNFLDhEQUFDdUU7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUN4RyxpRUFBT0E7Ozs7OzBCQUdSLDhEQUFDdUc7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDdkcsaUVBQU1BO3dCQUFDc0MsT0FBTTt3QkFBa0I4RCxhQUFhQTs7Ozs7O2tDQUc3Qyw4REFBQ0U7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUN0RyxvRUFBU0E7NEJBQ1JxQyxPQUFNOzRCQUNOa0UsT0FBTzVGLE1BQU02RixNQUFNOzRCQUNuQjVFLFNBQVNBOzRCQUNUd0QsTUFBTXpFOzRCQUNOZ0QsU0FBU0E7NEJBQ1Q4QyxVQUFVLElBQU0vRixlQUFlOzRCQUMvQmdHLGdCQUFlOzRCQUNmVixNQUFNQTs0QkFDTkUsVUFBVUE7NEJBQ1ZTLGNBQWNWOzRCQUNkVyxXQUFXN0Y7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1qQiw4REFBQ2Qsd0VBQVlBO2dCQUFDNEcsUUFBUXBHO2dCQUFhcUcsU0FBUyxJQUFNcEcsZUFBZTtnQkFBUXFHLFVBQVU3Qzs7Ozs7OzBCQUduRiw4REFBQzdELCtFQUFtQkE7Z0JBQ2xCd0csUUFBUW5GO2dCQUNSb0YsU0FBUyxJQUFNbkYsMEJBQTBCO2dCQUN6Q3FGLFNBQVN4Rjs7Ozs7OzBCQUdYLDhEQUFDakIsMkRBQU9BOzs7Ozs7Ozs7OztBQUdkO0dBek93QkM7O1FBU1BYLHNEQUFTQTtRQUNOUyxzREFBUUE7OztLQVZKRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxzcmNcXGFwcFxcYWRtaW5cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiXHJcbmltcG9ydCBTaWRlYmFyIGZyb20gXCJAL2NvbXBvbmVudHMvYWRtaW4vc2lkZWJhclwiXHJcbmltcG9ydCBUb3BOYXYgZnJvbSBcIkAvY29tcG9uZW50cy9hZG1pbi90b3AtbmF2XCJcclxuaW1wb3J0IERhdGFUYWJsZSBmcm9tIFwiQC9jb21wb25lbnRzL2FkbWluL2RhdGEtdGFibGVcIlxyXG5pbXBvcnQgQWRkVXNlck1vZGFsIGZyb20gXCJAL2NvbXBvbmVudHMvYWRtaW4vYWRkLXVzZXItbW9kYWxcIlxyXG5pbXBvcnQgeyBFeWUsIENsaXBib2FyZENvcHksIFRyYXNoMiB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxyXG5pbXBvcnQgU3R1ZGVudERldGFpbHNNb2RhbCBmcm9tIFwiQC9jb21wb25lbnRzL2FkbWluL3N0dWRlbnQtZGV0YWlscy1tb2RhbFwiXHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvaG9va3MvdXNlLXRvYXN0XCJcclxuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdG9hc3RlclwiXHJcblxyXG5pbnRlcmZhY2UgVXNlciB7XHJcbiAgX2lkOiBzdHJpbmdcclxuICBlbWFpbDogc3RyaW5nXHJcbiAgcGFzc3dvcmQ6IHN0cmluZ1xyXG4gIGRlY3J5cHRlZFBhc3N3b3JkPzogc3RyaW5nXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluRGFzaGJvYXJkKCkge1xyXG4gIGNvbnN0IFtpc01vZGFsT3Blbiwgc2V0SXNNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgW3VzZXJzLCBzZXRVc2Vyc10gPSB1c2VTdGF0ZTxVc2VyW10+KFtdKVxyXG4gIGNvbnN0IFt0b2tlbiwgc2V0VG9rZW5dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcclxuICBjb25zdCBbaXNMb2FkaW5nVXNlcnMsIHNldElzTG9hZGluZ1VzZXJzXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtkZWNyeXB0aW9uQ2FjaGUsIHNldERlY3J5cHRpb25DYWNoZV0gPSB1c2VTdGF0ZTxNYXA8c3RyaW5nLCBzdHJpbmc+PihuZXcgTWFwKCkpXHJcbiAgY29uc3QgaXNNb3VudGVkUmVmID0gdXNlUmVmKHRydWUpXHJcbiAgY29uc3QgaGFzRmV0Y2hlZFJlZiA9IHVzZVJlZihmYWxzZSlcclxuXHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcclxuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpXHJcblxyXG4gIGNvbnN0IFtzZWxlY3RlZFVzZXJGb3JEZXRhaWxzLCBzZXRTZWxlY3RlZFVzZXJGb3JEZXRhaWxzXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKVxyXG4gIGNvbnN0IFtpc1VzZXJEZXRhaWxzTW9kYWxPcGVuLCBzZXRJc1VzZXJEZXRhaWxzTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG5cclxuICBjb25zdCBjb2x1bW5zID0gW1xyXG4gICAgeyBrZXk6IFwiZW1haWxcIiwgbGFiZWw6IFwiTWFpbFwiIH0sXHJcbiAgICB7IGtleTogXCJkZWNyeXB0ZWRQYXNzd29yZFwiLCBsYWJlbDogXCJQYXNzd29yZFwiIH0sXHJcbiAgXVxyXG5cclxuICBjb25zdCBjb3B5VXNlckRldGFpbHMgPSAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4ge1xyXG4gICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQoYEVtYWlsOiAke2VtYWlsfVxcblBhc3N3b3JkOiAke3Bhc3N3b3JkfWApXHJcbiAgICB0b2FzdCh7IHRpdGxlOiBcIkNvcGllZCFcIiwgZGVzY3JpcHRpb246IFwiVXNlciBkZXRhaWxzIGNvcGllZCB0byBjbGlwYm9hcmRcIiB9KVxyXG4gIH1cclxuXHJcbiAgY29uc3QgZGVsZXRlVXNlciA9IGFzeW5jICh1c2VySWQ6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS91c2Vycz9pZD0ke3VzZXJJZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcclxuICAgICAgICBoZWFkZXJzOiBnZXRBdXRoSGVhZGVycygpLFxyXG4gICAgICB9KVxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgdXNlcicpXHJcbiAgICAgIHNldFVzZXJzKHVzZXJzLmZpbHRlcih1c2VyID0+IHVzZXIuX2lkICE9PSB1c2VySWQpKVxyXG4gICAgICB0b2FzdCh7IHRpdGxlOiAnU3VjY2VzcycsIGRlc2NyaXB0aW9uOiAnVXNlciBkZWxldGVkIHN1Y2Nlc3NmdWxseScgfSlcclxuICAgICAgLy8gUmVmcmVzaCB0aGUgdXNlciBsaXN0IHRvIGVuc3VyZSBjb25zaXN0ZW5jeVxyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHJlZnJlc2hVc2VycygpLCAxMDAwKVxyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgdXNlcjonLCBlcnIpXHJcbiAgICAgIHRvYXN0KHsgdGl0bGU6ICdFcnJvcicsIGRlc2NyaXB0aW9uOiAnQ291bGQgbm90IGRlbGV0ZSB1c2VyJyB9KVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgYWN0aW9ucyA9IFtcclxuICAgIHtcclxuICAgICAgaWNvbjogRXllLFxyXG4gICAgICBsYWJlbDogJ1ZpZXcgRGV0YWlscycsXHJcbiAgICAgIG9uQ2xpY2s6IChyb3c6IFVzZXIpID0+IHtcclxuICAgICAgICBzZXRTZWxlY3RlZFVzZXJGb3JEZXRhaWxzKHJvdylcclxuICAgICAgICBzZXRJc1VzZXJEZXRhaWxzTW9kYWxPcGVuKHRydWUpXHJcbiAgICAgIH0sXHJcbiAgICAgIHZhcmlhbnQ6ICd2aWV3JyBhcyBjb25zdCxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGljb246IENsaXBib2FyZENvcHksXHJcbiAgICAgIGxhYmVsOiAnQ29weScsXHJcbiAgICAgIG9uQ2xpY2s6IChyb3c6IFVzZXIpID0+IGNvcHlVc2VyRGV0YWlscyhyb3cuZW1haWwsIHJvdy5kZWNyeXB0ZWRQYXNzd29yZCB8fCByb3cucGFzc3dvcmQpLFxyXG4gICAgICB2YXJpYW50OiAnZWRpdCcgYXMgY29uc3QsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpY29uOiBUcmFzaDIsXHJcbiAgICAgIGxhYmVsOiAnRGVsZXRlJyxcclxuICAgICAgb25DbGljazogKHJvdzogVXNlcikgPT4gZGVsZXRlVXNlcihyb3cuX2lkKSxcclxuICAgICAgdmFyaWFudDogJ2RlbGV0ZScgYXMgY29uc3QsXHJcbiAgICB9LFxyXG4gIF1cclxuXHJcbiAgY29uc3QgcmVmcmVzaFVzZXJzID0gKCkgPT4ge1xyXG4gICAgZmV0Y2hVc2VycygpXHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVBZGRVc2VyID0gYXN5bmMgKHsgZW1haWwsIHBhc3N3b3JkIH06IHsgZW1haWw6IHN0cmluZzsgcGFzc3dvcmQ6IHN0cmluZyB9KSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9L2F1dGgvcmVnaXN0ZXJgLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczogZ2V0QXV0aEhlYWRlcnMoKSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IGVtYWlsLCBwYXNzd29yZCB9KSxcclxuICAgICAgfSlcclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdSZWdpc3RyYXRpb24gZmFpbGVkJylcclxuICAgICAgY29uc3QgbmV3VXNlciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxyXG4gICAgICBjb25zdCBkZWNyeXB0ZWRQYXNzd29yZCA9IGF3YWl0IGRlY3J5cHRQYXNzd29yZChwYXNzd29yZClcclxuICAgICAgc2V0VXNlcnMoWy4uLnVzZXJzLCB7IF9pZDogbmV3VXNlci5faWQsIGVtYWlsLCBwYXNzd29yZCwgZGVjcnlwdGVkUGFzc3dvcmQgfV0pXHJcbiAgICAgIHRvYXN0KHsgdGl0bGU6ICdTdWNjZXNzJywgZGVzY3JpcHRpb246ICdVc2VyIHJlZ2lzdGVyZWQgc3VjY2Vzc2Z1bGx5JyB9KVxyXG4gICAgICAvLyBSZWZyZXNoIHRoZSB1c2VyIGxpc3QgdG8gZW5zdXJlIGNvbnNpc3RlbmN5XHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVmcmVzaFVzZXJzKCksIDEwMDApXHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHJlZ2lzdGVyIHVzZXI6JywgZXJyKVxyXG4gICAgICB0b2FzdCh7IHRpdGxlOiAnRXJyb3InLCBkZXNjcmlwdGlvbjogJ0ZhaWxlZCB0byByZWdpc3RlciB1c2VyJyB9KVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gVG9rZW4gcmV0cmlldmFsXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWNjZXNzVG9rZW4nKVxyXG4gICAgICBpZiAoIXN0b3JlZCkge1xyXG4gICAgICAgIHJvdXRlci5wdXNoKCcvYWRtaW4tbG9naW4nKVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldFRva2VuKHN0b3JlZClcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFtyb3V0ZXJdKVxyXG5cclxuICBjb25zdCBnZXRBdXRoSGVhZGVycyA9IHVzZUNhbGxiYWNrKCgpID0+ICh7XHJcbiAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgfSksIFt0b2tlbl0pXHJcblxyXG4gIGNvbnN0IGRlY3J5cHRQYXNzd29yZCA9IGFzeW5jIChlbmNyeXB0ZWQ6IHN0cmluZykgPT4ge1xyXG4gICAgLy8gUmV0dXJuIGVhcmx5IGlmIG5vIGVuY3J5cHRlZCBwYXNzd29yZCBvciBlbXB0eSBzdHJpbmdcclxuICAgIGlmICghZW5jcnlwdGVkIHx8IGVuY3J5cHRlZC50cmltKCkgPT09ICcnKSB7XHJcbiAgICAgIHJldHVybiBlbmNyeXB0ZWRcclxuICAgIH1cclxuXHJcbiAgICAvLyBDaGVjayBjYWNoZSBmaXJzdFxyXG4gICAgaWYgKGRlY3J5cHRpb25DYWNoZS5oYXMoZW5jcnlwdGVkKSkge1xyXG4gICAgICByZXR1cm4gZGVjcnlwdGlvbkNhY2hlLmdldChlbmNyeXB0ZWQpIVxyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKCcvYXBpL2RlY3J5cHQnLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBlbmNyeXB0ZWRQYXNzd29yZDogZW5jcnlwdGVkIH0pLFxyXG4gICAgICB9KVxyXG4gICAgICBpZiAoIXJlcy5vaykgdGhyb3cgbmV3IEVycm9yKCdkZWNyeXB0JylcclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5qc29uKClcclxuXHJcbiAgICAgIC8vIENhY2hlIHRoZSByZXN1bHQgb25seSBpZiBjb21wb25lbnQgaXMgc3RpbGwgbW91bnRlZFxyXG4gICAgICBpZiAoaXNNb3VudGVkUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBzZXREZWNyeXB0aW9uQ2FjaGUocHJldiA9PiBuZXcgTWFwKHByZXYpLnNldChlbmNyeXB0ZWQsIGRhdGEuZGVjcnlwdGVkUGFzc3dvcmQpKVxyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gZGF0YS5kZWNyeXB0ZWRQYXNzd29yZFxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRGVjcnlwdGlvbiBmYWlsZWQgZm9yOicsIGVuY3J5cHRlZCwgZXJyb3IpXHJcbiAgICAgIHJldHVybiBlbmNyeXB0ZWRcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGZldGNoVXNlcnMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoaXNMb2FkaW5nVXNlcnMgfHwgIXRva2VuKSByZXR1cm5cclxuXHJcbiAgICBzZXRJc0xvYWRpbmdVc2Vycyh0cnVlKVxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgaGVhZGVycyA9IGdldEF1dGhIZWFkZXJzKClcclxuICAgICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTH0vdXNlcnNgLCB7IGhlYWRlcnMgfSlcclxuXHJcbiAgICAgIGlmICghcmVzLm9rKSB7XHJcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzLnRleHQoKVxyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJBZG1pbiBwYWdlIC0gVXNlcnMgZmV0Y2ggZmFpbGVkOlwiLCBlcnJvclRleHQpXHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBmZXRjaCBmYWlsZWQ6ICR7cmVzLnN0YXR1c31gKVxyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBkYXRhOiBVc2VyW10gPSBhd2FpdCByZXMuanNvbigpXHJcbiAgICAgIGNvbnN0IGxpc3QgPSBhd2FpdCBQcm9taXNlLmFsbChcclxuICAgICAgICBkYXRhLm1hcChhc3luYyB1ID0+ICh7IC4uLnUsIGRlY3J5cHRlZFBhc3N3b3JkOiBhd2FpdCBkZWNyeXB0UGFzc3dvcmQodS5wYXNzd29yZCkgfSkpXHJcbiAgICAgIClcclxuXHJcbiAgICAgIGlmIChpc01vdW50ZWRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIHNldFVzZXJzKGxpc3QpXHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJBZG1pbiBwYWdlIC0gRXJyb3IgZmV0Y2hpbmcgdXNlcnM6XCIsIGVycm9yKVxyXG4gICAgICBpZiAoaXNNb3VudGVkUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICB0b2FzdCh7IHRpdGxlOiAnRXJyb3InLCBkZXNjcmlwdGlvbjogJ0ZhaWxlZCB0byBsb2FkIHVzZXJzJyB9KVxyXG4gICAgICB9XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBpZiAoaXNNb3VudGVkUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBzZXRJc0xvYWRpbmdVc2VycyhmYWxzZSlcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFt0b2tlbl0pIC8vIE9ubHkgZGVwZW5kIG9uIHRva2VuIHRvIHByZXZlbnQgaW5maW5pdGUgbG9vcHNcclxuXHJcbiAgLy8gRmV0Y2ggdXNlcnMgd2hlbiB0b2tlbiBpcyBhdmFpbGFibGVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHRva2VuKSB7XHJcbiAgICAgIGZldGNoVXNlcnMoKVxyXG4gICAgfVxyXG4gIH0sIFt0b2tlbiwgZmV0Y2hVc2Vyc10pXHJcblxyXG4gIC8vIENsZWFudXAgZWZmZWN0XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGlzTW91bnRlZFJlZi5jdXJyZW50ID0gZmFsc2VcclxuICAgIH1cclxuICB9LCBbXSlcclxuXHJcbiAgY29uc3QgW3BhZ2UsIHNldFBhZ2VdID0gdXNlU3RhdGUoMSlcclxuICBjb25zdCBwYWdlU2l6ZSA9IDEwXHJcblxyXG4gIGNvbnN0IGJyZWFkY3J1bWJzID0gW3sgbGFiZWw6IFwiQWRtaW4gRGFzaGJvYXJkXCIsIGhyZWY6IFwiL2Rhc2hib2FyZFwiIH0sIHsgbGFiZWw6IFwiU3R1ZGVudCBEZXRhaWxzXCIgfV1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleFwiPlxyXG4gICAgICB7LyogU2lkZWJhciAqL31cclxuICAgICAgPFNpZGViYXIgLz5cclxuXHJcbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2xcIj5cclxuICAgICAgICB7LyogVG9wIE5hdmlnYXRpb24gKi99XHJcbiAgICAgICAgPFRvcE5hdiB0aXRsZT1cIkFkbWluIERhc2hib2FyZFwiIGJyZWFkY3J1bWJzPXticmVhZGNydW1ic30gLz5cclxuXHJcbiAgICAgICAgey8qIENvbnRlbnQgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC02XCI+XHJcbiAgICAgICAgICA8RGF0YVRhYmxlXHJcbiAgICAgICAgICAgIHRpdGxlPVwiU3R1ZGVudHNcIlxyXG4gICAgICAgICAgICBjb3VudD17dXNlcnMubGVuZ3RofVxyXG4gICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgICBkYXRhPXt1c2VycyBhcyB1bmtub3duIGFzIFJlY29yZDxzdHJpbmcsIHVua25vd24+W119XHJcbiAgICAgICAgICAgIGFjdGlvbnM9e2FjdGlvbnMgYXMgdW5rbm93biBhcyBBcnJheTx7IGljb246IFJlYWN0LkNvbXBvbmVudFR5cGU8eyBjbGFzc05hbWU/OiBzdHJpbmcgfT47IGxhYmVsOiBzdHJpbmc7IG9uQ2xpY2s6IChyb3c6IFJlY29yZDxzdHJpbmcsIHVua25vd24+KSA9PiB2b2lkOyB2YXJpYW50PzogXCJkZWZhdWx0XCIgfCBcImVkaXRcIiB8IFwiZGVsZXRlXCIgfCBcInZpZXdcIiB9Pn1cclxuICAgICAgICAgICAgb25BZGROZXc9eygpID0+IHNldElzTW9kYWxPcGVuKHRydWUpfVxyXG4gICAgICAgICAgICBhZGRCdXR0b25MYWJlbD1cIkFkZCBVc2VyXCJcclxuICAgICAgICAgICAgcGFnZT17cGFnZX1cclxuICAgICAgICAgICAgcGFnZVNpemU9e3BhZ2VTaXplfVxyXG4gICAgICAgICAgICBvblBhZ2VDaGFuZ2U9e3NldFBhZ2V9XHJcbiAgICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nVXNlcnN9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBBZGQgVXNlciBNb2RhbCAqL31cclxuICAgICAgPEFkZFVzZXJNb2RhbCBpc09wZW49e2lzTW9kYWxPcGVufSBvbkNsb3NlPXsoKSA9PiBzZXRJc01vZGFsT3BlbihmYWxzZSl9IG9uU3VibWl0PXtoYW5kbGVBZGRVc2VyfSAvPlxyXG5cclxuICAgICAgey8qIFN0dWRlbnQgRGV0YWlscyBNb2RhbCAqL31cclxuICAgICAgPFN0dWRlbnREZXRhaWxzTW9kYWxcclxuICAgICAgICBpc09wZW49e2lzVXNlckRldGFpbHNNb2RhbE9wZW59XHJcbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNVc2VyRGV0YWlsc01vZGFsT3BlbihmYWxzZSl9XHJcbiAgICAgICAgc3R1ZGVudD17c2VsZWN0ZWRVc2VyRm9yRGV0YWlsc31cclxuICAgICAgLz5cclxuXHJcbiAgICAgIDxUb2FzdGVyIC8+XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJ1c2VSb3V0ZXIiLCJTaWRlYmFyIiwiVG9wTmF2IiwiRGF0YVRhYmxlIiwiQWRkVXNlck1vZGFsIiwiRXllIiwiQ2xpcGJvYXJkQ29weSIsIlRyYXNoMiIsIlN0dWRlbnREZXRhaWxzTW9kYWwiLCJ1c2VUb2FzdCIsIlRvYXN0ZXIiLCJBZG1pbkRhc2hib2FyZCIsImlzTW9kYWxPcGVuIiwic2V0SXNNb2RhbE9wZW4iLCJ1c2VycyIsInNldFVzZXJzIiwidG9rZW4iLCJzZXRUb2tlbiIsImlzTG9hZGluZ1VzZXJzIiwic2V0SXNMb2FkaW5nVXNlcnMiLCJkZWNyeXB0aW9uQ2FjaGUiLCJzZXREZWNyeXB0aW9uQ2FjaGUiLCJNYXAiLCJpc01vdW50ZWRSZWYiLCJoYXNGZXRjaGVkUmVmIiwicm91dGVyIiwidG9hc3QiLCJzZWxlY3RlZFVzZXJGb3JEZXRhaWxzIiwic2V0U2VsZWN0ZWRVc2VyRm9yRGV0YWlscyIsImlzVXNlckRldGFpbHNNb2RhbE9wZW4iLCJzZXRJc1VzZXJEZXRhaWxzTW9kYWxPcGVuIiwiY29sdW1ucyIsImtleSIsImxhYmVsIiwiY29weVVzZXJEZXRhaWxzIiwiZW1haWwiLCJwYXNzd29yZCIsIm5hdmlnYXRvciIsImNsaXBib2FyZCIsIndyaXRlVGV4dCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkZWxldGVVc2VyIiwidXNlcklkIiwicmVzcG9uc2UiLCJmZXRjaCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwibWV0aG9kIiwiaGVhZGVycyIsImdldEF1dGhIZWFkZXJzIiwib2siLCJFcnJvciIsImZpbHRlciIsInVzZXIiLCJfaWQiLCJzZXRUaW1lb3V0IiwicmVmcmVzaFVzZXJzIiwiZXJyIiwiY29uc29sZSIsImVycm9yIiwiYWN0aW9ucyIsImljb24iLCJvbkNsaWNrIiwicm93IiwidmFyaWFudCIsImRlY3J5cHRlZFBhc3N3b3JkIiwiZmV0Y2hVc2VycyIsImhhbmRsZUFkZFVzZXIiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm5ld1VzZXIiLCJqc29uIiwiZGVjcnlwdFBhc3N3b3JkIiwic3RvcmVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInB1c2giLCJBdXRob3JpemF0aW9uIiwiZW5jcnlwdGVkIiwidHJpbSIsImhhcyIsImdldCIsInJlcyIsImVuY3J5cHRlZFBhc3N3b3JkIiwiZGF0YSIsImN1cnJlbnQiLCJwcmV2Iiwic2V0IiwiZXJyb3JUZXh0IiwidGV4dCIsInN0YXR1cyIsImxpc3QiLCJQcm9taXNlIiwiYWxsIiwibWFwIiwidSIsInBhZ2UiLCJzZXRQYWdlIiwicGFnZVNpemUiLCJicmVhZGNydW1icyIsImhyZWYiLCJkaXYiLCJjbGFzc05hbWUiLCJjb3VudCIsImxlbmd0aCIsIm9uQWRkTmV3IiwiYWRkQnV0dG9uTGFiZWwiLCJvblBhZ2VDaGFuZ2UiLCJpc0xvYWRpbmciLCJpc09wZW4iLCJvbkNsb3NlIiwib25TdWJtaXQiLCJzdHVkZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});
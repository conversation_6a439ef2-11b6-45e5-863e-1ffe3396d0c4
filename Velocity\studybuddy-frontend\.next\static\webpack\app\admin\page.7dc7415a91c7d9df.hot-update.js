"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/sidebar */ \"(app-pages-browser)/./src/components/admin/sidebar.tsx\");\n/* harmony import */ var _components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/top-nav */ \"(app-pages-browser)/./src/components/admin/top-nav.tsx\");\n/* harmony import */ var _components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/data-table */ \"(app-pages-browser)/./src/components/admin/data-table.tsx\");\n/* harmony import */ var _components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/add-user-modal */ \"(app-pages-browser)/./src/components/admin/add-user-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-copy.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/admin/student-details-modal */ \"(app-pages-browser)/./src/components/admin/student-details-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingUsers, setIsLoadingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [decryptionCache, setDecryptionCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const hasFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [selectedUserForDetails, setSelectedUserForDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUserDetailsModalOpen, setIsUserDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const columns = [\n        {\n            key: \"email\",\n            label: \"Mail\"\n        },\n        {\n            key: \"decryptedPassword\",\n            label: \"Password\"\n        }\n    ];\n    const copyUserDetails = (email, password)=>{\n        navigator.clipboard.writeText(\"Email: \".concat(email, \"\\nPassword: \").concat(password));\n        toast({\n            title: \"Copied!\",\n            description: \"User details copied to clipboard\"\n        });\n    };\n    const deleteUser = async (userId)=>{\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/users?id=\").concat(userId), {\n                method: 'DELETE',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) throw new Error('Failed to delete user');\n            setUsers(users.filter((user)=>user._id !== userId));\n            toast({\n                title: 'Success',\n                description: 'User deleted successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to delete user:', err);\n            toast({\n                title: 'Error',\n                description: 'Could not delete user'\n            });\n        }\n    };\n    const actions = [\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'View Details',\n            onClick: (row)=>{\n                setSelectedUserForDetails(row);\n                setIsUserDetailsModalOpen(true);\n            },\n            variant: 'view'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            label: 'Copy',\n            onClick: (row)=>copyUserDetails(row.email, row.decryptedPassword || row.password),\n            variant: 'edit'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            label: 'Delete',\n            onClick: (row)=>deleteUser(row._id),\n            variant: 'delete'\n        }\n    ];\n    const refreshUsers = ()=>{\n        hasFetchedRef.current = false;\n        fetchUsers();\n    };\n    const handleAddUser = async (param)=>{\n        let { email, password } = param;\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/auth/register\"), {\n                method: 'POST',\n                headers: getAuthHeaders(),\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) throw new Error('Registration failed');\n            const newUser = await response.json();\n            const decryptedPassword = await decryptPassword(password);\n            setUsers([\n                ...users,\n                {\n                    _id: newUser._id,\n                    email,\n                    password,\n                    decryptedPassword\n                }\n            ]);\n            toast({\n                title: 'Success',\n                description: 'User registered successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to register user:', err);\n            toast({\n                title: 'Error',\n                description: 'Failed to register user'\n            });\n        }\n    };\n    // Token retrieval\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (true) {\n                const stored = localStorage.getItem('accessToken');\n                if (!stored) {\n                    router.push('/admin-login');\n                } else {\n                    setToken(stored);\n                    hasFetchedRef.current = false // Reset fetch flag when token changes\n                    ;\n                }\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        router\n    ]);\n    const getAuthHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[getAuthHeaders]\": ()=>({\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'application/json'\n            })\n    }[\"AdminDashboard.useCallback[getAuthHeaders]\"], [\n        token\n    ]);\n    const decryptPassword = async (encrypted)=>{\n        // Return early if no encrypted password or empty string\n        if (!encrypted || encrypted.trim() === '') {\n            return encrypted;\n        }\n        // Check cache first\n        if (decryptionCache.has(encrypted)) {\n            return decryptionCache.get(encrypted);\n        }\n        try {\n            const res = await fetch('/api/decrypt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    encryptedPassword: encrypted\n                })\n            });\n            if (!res.ok) throw new Error('decrypt');\n            const data = await res.json();\n            // Cache the result only if component is still mounted\n            if (isMountedRef.current) {\n                setDecryptionCache((prev)=>new Map(prev).set(encrypted, data.decryptedPassword));\n            }\n            return data.decryptedPassword;\n        } catch (error) {\n            console.error('Decryption failed for:', encrypted, error);\n            return encrypted;\n        }\n    };\n    const fetchUsers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[fetchUsers]\": async ()=>{\n            if (isLoadingUsers || !token || hasFetchedRef.current) return;\n            setIsLoadingUsers(true);\n            try {\n                const headers = getAuthHeaders();\n                const res = await fetch(\"\".concat(\"http://localhost:3000\", \"/users\"), {\n                    headers,\n                    cache: 'no-cache' // Force fresh data, avoid 304 responses\n                });\n                if (!res.ok) {\n                    const errorText = await res.text();\n                    console.error(\"Admin page - Users fetch failed:\", errorText);\n                    throw new Error(\"fetch failed: \".concat(res.status));\n                }\n                // Handle 304 Not Modified - data hasn't changed, but we still need to show existing data\n                if (res.status === 304) {\n                    console.log(\"Data not modified (304), keeping existing data\");\n                    return;\n                }\n                const data = await res.json();\n                console.log(\"Fetched users data:\", data) // Debug log\n                ;\n                const list = await Promise.all(data.map({\n                    \"AdminDashboard.useCallback[fetchUsers]\": async (u)=>({\n                            ...u,\n                            decryptedPassword: await decryptPassword(u.password)\n                        })\n                }[\"AdminDashboard.useCallback[fetchUsers]\"]));\n                if (isMountedRef.current) {\n                    setUsers(list);\n                    hasFetchedRef.current = true;\n                    console.log(\"Updated users state with:\", list.length, \"users\") // Debug log\n                    ;\n                }\n            } catch (error) {\n                console.error(\"Admin page - Error fetching users:\", error);\n                if (isMountedRef.current) {\n                    toast({\n                        title: 'Error',\n                        description: 'Failed to load users'\n                    });\n                }\n            } finally{\n                if (isMountedRef.current) {\n                    setIsLoadingUsers(false);\n                }\n            }\n        }\n    }[\"AdminDashboard.useCallback[fetchUsers]\"], [\n        token\n    ]) // Only depend on token to prevent infinite loops\n    ;\n    // Fetch users when token is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (token) {\n                console.log(\"Token available, fetching users...\") // Debug log\n                ;\n                fetchUsers();\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        token,\n        fetchUsers\n    ]);\n    // Debug log for users state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            console.log(\"Users state changed:\", users.length, \"users\", users) // Debug log\n            ;\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        users\n    ]);\n    // Cleanup effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            return ({\n                \"AdminDashboard.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"AdminDashboard.useEffect\"];\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const pageSize = 10;\n    const breadcrumbs = [\n        {\n            label: \"Admin Dashboard\",\n            href: \"/dashboard\"\n        },\n        {\n            label: \"Student Details\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: \"Admin Dashboard\",\n                        breadcrumbs: breadcrumbs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            title: \"Students\",\n                            count: users.length,\n                            columns: columns,\n                            data: users,\n                            actions: actions,\n                            onAddNew: ()=>setIsModalOpen(true),\n                            addButtonLabel: \"Add User\",\n                            page: page,\n                            pageSize: pageSize,\n                            onPageChange: setPage,\n                            isLoading: isLoadingUsers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                onSubmit: handleAddUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isUserDetailsModalOpen,\n                onClose: ()=>setIsUserDetailsModalOpen(false),\n                student: selectedUserForDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"lrPOBgg3ijKvAKf41YSdoc/c+Ok=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});
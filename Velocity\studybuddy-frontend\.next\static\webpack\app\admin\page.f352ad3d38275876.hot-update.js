"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/sidebar */ \"(app-pages-browser)/./src/components/admin/sidebar.tsx\");\n/* harmony import */ var _components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/top-nav */ \"(app-pages-browser)/./src/components/admin/top-nav.tsx\");\n/* harmony import */ var _components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/data-table */ \"(app-pages-browser)/./src/components/admin/data-table.tsx\");\n/* harmony import */ var _components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/add-user-modal */ \"(app-pages-browser)/./src/components/admin/add-user-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-copy.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/admin/student-details-modal */ \"(app-pages-browser)/./src/components/admin/student-details-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingUsers, setIsLoadingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [decryptionCache, setDecryptionCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [selectedUserForDetails, setSelectedUserForDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUserDetailsModalOpen, setIsUserDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const columns = [\n        {\n            key: \"email\",\n            label: \"Mail\"\n        },\n        {\n            key: \"decryptedPassword\",\n            label: \"Password\"\n        }\n    ];\n    const copyUserDetails = (email, password)=>{\n        navigator.clipboard.writeText(\"Email: \".concat(email, \"\\nPassword: \").concat(password));\n        toast({\n            title: \"Copied!\",\n            description: \"User details copied to clipboard\"\n        });\n    };\n    const deleteUser = async (userId)=>{\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/users?id=\").concat(userId), {\n                method: 'DELETE',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) throw new Error('Failed to delete user');\n            setUsers(users.filter((user)=>user._id !== userId));\n            toast({\n                title: 'Success',\n                description: 'User deleted successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to delete user:', err);\n            toast({\n                title: 'Error',\n                description: 'Could not delete user'\n            });\n        }\n    };\n    const actions = [\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'View Details',\n            onClick: (row)=>{\n                setSelectedUserForDetails(row);\n                setIsUserDetailsModalOpen(true);\n            },\n            variant: 'view'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            label: 'Copy',\n            onClick: (row)=>copyUserDetails(row.email, row.decryptedPassword || row.password),\n            variant: 'edit'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            label: 'Delete',\n            onClick: (row)=>deleteUser(row._id),\n            variant: 'delete'\n        }\n    ];\n    const refreshUsers = ()=>{\n        fetchUsers();\n    };\n    const handleAddUser = async (param)=>{\n        let { email, password } = param;\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/auth/register\"), {\n                method: 'POST',\n                headers: getAuthHeaders(),\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) throw new Error('Registration failed');\n            const newUser = await response.json();\n            const decryptedPassword = await decryptPassword(password);\n            setUsers([\n                ...users,\n                {\n                    _id: newUser._id,\n                    email,\n                    password,\n                    decryptedPassword\n                }\n            ]);\n            toast({\n                title: 'Success',\n                description: 'User registered successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to register user:', err);\n            toast({\n                title: 'Error',\n                description: 'Failed to register user'\n            });\n        }\n    };\n    // Token retrieval\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (true) {\n                const stored = localStorage.getItem('accessToken');\n                if (!stored) {\n                    router.push('/admin-login');\n                } else {\n                    setToken(stored);\n                }\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        router\n    ]);\n    const getAuthHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[getAuthHeaders]\": ()=>({\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'application/json'\n            })\n    }[\"AdminDashboard.useCallback[getAuthHeaders]\"], [\n        token\n    ]);\n    const decryptPassword = async (encrypted)=>{\n        // Return early if no encrypted password or empty string\n        if (!encrypted || encrypted.trim() === '') {\n            return encrypted;\n        }\n        // Check cache first\n        if (decryptionCache.has(encrypted)) {\n            return decryptionCache.get(encrypted);\n        }\n        try {\n            const res = await fetch('/api/decrypt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    encryptedPassword: encrypted\n                })\n            });\n            if (!res.ok) throw new Error('decrypt');\n            const data = await res.json();\n            // Cache the result only if component is still mounted\n            if (isMountedRef.current) {\n                setDecryptionCache((prev)=>new Map(prev).set(encrypted, data.decryptedPassword));\n            }\n            return data.decryptedPassword;\n        } catch (error) {\n            console.error('Decryption failed for:', encrypted, error);\n            return encrypted;\n        }\n    };\n    const fetchUsers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[fetchUsers]\": async ()=>{\n            if (isLoadingUsers || !token) return; // Prevent multiple simultaneous calls and ensure token exists\n            // Check if we recently fetched (within last 30 seconds) to handle hot reloads\n            const lastFetchTime = localStorage.getItem('admin_last_fetch_time');\n            const now = Date.now();\n            if (lastFetchTime && now - parseInt(lastFetchTime) < 30000) {\n                return;\n            }\n            // Prevent fetching if we already fetched with the same token\n            if (lastFetchTokenRef.current === token && hasFetchedRef.current) {\n                return;\n            }\n            setIsLoadingUsers(true);\n            lastFetchTokenRef.current = token;\n            localStorage.setItem('admin_last_fetch_time', now.toString());\n            try {\n                // //console.log(\"Admin page - Fetching users with token:\", token ? \"Token present\" : \"No token\")\n                const headers = getAuthHeaders();\n                // //console.log(\"Admin page - Auth headers:\", headers)\n                const res = await fetch(\"\".concat(\"http://localhost:3000\", \"/users\"), {\n                    headers\n                });\n                // //console.log(\"Admin page - Users fetch response status:\", res.status)\n                if (!res.ok) {\n                    const errorText = await res.text();\n                    console.error(\"Admin page - Users fetch failed:\", errorText);\n                    throw new Error(\"fetch failed: \".concat(res.status));\n                }\n                const data = await res.json();\n                // //console.log(\"Admin page - Users data received:\", data.length, \"users\")\n                const list = await Promise.all(data.map({\n                    \"AdminDashboard.useCallback[fetchUsers]\": async (u)=>({\n                            ...u,\n                            decryptedPassword: await decryptPassword(u.password)\n                        })\n                }[\"AdminDashboard.useCallback[fetchUsers]\"]));\n                // Only update state if component is still mounted\n                if (isMountedRef.current) {\n                    setUsers(list);\n                    hasFetchedRef.current = true;\n                }\n            } catch (error) {\n                console.error(\"Admin page - Error fetching users:\", error);\n                if (isMountedRef.current) {\n                    toast({\n                        title: 'Error',\n                        description: 'Failed to load users'\n                    });\n                }\n            } finally{\n                if (isMountedRef.current) {\n                    setIsLoadingUsers(false);\n                }\n            }\n        }\n    }[\"AdminDashboard.useCallback[fetchUsers]\"], [\n        token\n    ]) // Remove all other dependencies to prevent infinite loop\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (token) {\n                // Clear any existing timeout\n                if (fetchTimeoutRef.current) {\n                    clearTimeout(fetchTimeoutRef.current);\n                }\n                // Debounce the fetchUsers call\n                fetchTimeoutRef.current = setTimeout({\n                    \"AdminDashboard.useEffect\": ()=>{\n                        fetchUsers();\n                    }\n                }[\"AdminDashboard.useEffect\"], 100) // 100ms debounce\n                ;\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        token,\n        fetchUsers\n    ]);\n    // Cleanup effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            return ({\n                \"AdminDashboard.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                    if (fetchTimeoutRef.current) {\n                        clearTimeout(fetchTimeoutRef.current);\n                    }\n                }\n            })[\"AdminDashboard.useEffect\"];\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const pageSize = 10;\n    const breadcrumbs = [\n        {\n            label: \"Admin Dashboard\",\n            href: \"/dashboard\"\n        },\n        {\n            label: \"Student Details\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: \"Admin Dashboard\",\n                        breadcrumbs: breadcrumbs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            title: \"Students\",\n                            count: users.length,\n                            columns: columns,\n                            data: users,\n                            actions: actions,\n                            onAddNew: ()=>setIsModalOpen(true),\n                            addButtonLabel: \"Add User\",\n                            page: page,\n                            pageSize: pageSize,\n                            onPageChange: setPage,\n                            isLoading: isLoadingUsers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                onSubmit: handleAddUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isUserDetailsModalOpen,\n                onClose: ()=>setIsUserDetailsModalOpen(false),\n                student: selectedUserForDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"2N4N6rapZAr566sxuKUpG8sFeX8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});